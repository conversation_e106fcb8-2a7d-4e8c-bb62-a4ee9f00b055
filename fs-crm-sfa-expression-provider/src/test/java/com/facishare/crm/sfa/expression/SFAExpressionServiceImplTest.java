package com.facishare.crm.sfa.expression;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.IFieldType;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SFAExpressionServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2025/01/16
 */
@ExtendWith(MockitoExtension.class)
class SFAExpressionServiceImplTest {
    
    @Mock
    private ExpressionService expressionService;
    
    @Mock
    private IObjectData objectData;
    
    @Mock
    private IObjectDescribe objectDescribe;
    
    @Mock
    private IFieldDescribe fieldDescribe;
    
    @InjectMocks
    private SFAExpressionServiceImpl sfaExpressionService;
    
    @BeforeEach
    void setUp() {
        // 设置基本的mock行为
        when(objectDescribe.getFieldDescribes()).thenReturn(Lists.newArrayList(fieldDescribe));
        when(fieldDescribe.getApiName()).thenReturn("owner_department");
        when(fieldDescribe.getType()).thenReturn(IFieldType.DEPARTMENT);
    }
    
    @Test
    void testEvaluate_DepartmentField_WithCascade() {
        // 准备测试数据 - 确保字段类型为 DEPARTMENT
        when(fieldDescribe.getType()).thenReturn(IFieldType.DEPARTMENT);

        String expression = "owner_department IN ('1000') AND is_cascade = true";

        // 创建带有 is_cascade 的过滤器
        Filter filter = new Filter();
        filter.setFieldName("owner_department");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList("1000"));
        filter.setIsCascade(true); // 设置级联标志

        // 模拟表达式服务返回
        when(expressionService.evaluate(anyString(), eq(objectData))).thenReturn(true);

        // 执行测试
        Boolean result = sfaExpressionService.evaluate(expression, objectData, objectDescribe);

        // 验证结果
        assertNotNull(result);
        assertTrue(result);

        // 验证表达式服务被调用，并且应该使用 INDEPARTMENT 函数
        verify(expressionService, times(1)).evaluate(contains("INDEPARTMENT"), eq(objectData));
    }
    
    @Test
    void testEvaluate_DepartmentField_WithoutCascade() {
        // 准备测试数据
        String expression = "owner_department IN ('1000')";
        
        // 创建不带 is_cascade 的过滤器
        Filter filter = new Filter();
        filter.setFieldName("owner_department");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList("1000"));
        filter.setIsCascade(false); // 不设置级联标志
        
        // 模拟表达式服务返回
        when(expressionService.evaluate(anyString(), eq(objectData))).thenReturn(true);
        
        // 执行测试
        Boolean result = sfaExpressionService.evaluate(expression, objectData, objectDescribe);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result);
        
        // 验证表达式服务被调用
        verify(expressionService, times(1)).evaluate(anyString(), eq(objectData));
    }
    
    @Test
    void testEvaluate_DepartmentField_NIN_WithCascade() {
        // 准备测试数据
        String expression = "owner_department NIN ('1000')";
        
        // 创建带有 is_cascade 的 NIN 过滤器
        Filter filter = new Filter();
        filter.setFieldName("owner_department");
        filter.setOperator(Operator.NIN);
        filter.setFieldValues(Lists.newArrayList("1000"));
        filter.setIsCascade(true); // 设置级联标志
        
        // 模拟表达式服务返回
        when(expressionService.evaluate(anyString(), eq(objectData))).thenReturn(false);
        
        // 执行测试
        Boolean result = sfaExpressionService.evaluate(expression, objectData, objectDescribe);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result);
        
        // 验证表达式服务被调用
        verify(expressionService, times(1)).evaluate(anyString(), eq(objectData));
    }
    
    @Test
    void testEvaluate_NonDepartmentField_WithCascade() {
        // 设置非部门字段（员工字段）
        when(fieldDescribe.getApiName()).thenReturn("owner");
        when(fieldDescribe.getType()).thenReturn(IFieldType.EMPLOYEE);

        // 准备测试数据 - 即使设置了 is_cascade，非部门字段也不应该使用级联逻辑
        String expression = "owner IN ('1000') AND is_cascade = true";

        // 创建带有 is_cascade 的过滤器，但字段类型不是 DEPARTMENT
        Filter filter = new Filter();
        filter.setFieldName("owner");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList("1000"));
        filter.setIsCascade(true); // 设置级联标志，但应该被忽略

        // 模拟表达式服务返回
        when(expressionService.evaluate(anyString(), eq(objectData))).thenReturn(true);

        // 执行测试
        Boolean result = sfaExpressionService.evaluate(expression, objectData, objectDescribe);

        // 验证结果
        assertNotNull(result);
        assertTrue(result);

        // 验证表达式服务被调用，但不应该使用 INDEPARTMENT 函数
        verify(expressionService, times(1)).evaluate(not(contains("INDEPARTMENT")), eq(objectData));
    }

    @Test
    void testEvaluate_NonDepartmentField() {
        // 设置非部门字段
        when(fieldDescribe.getApiName()).thenReturn("name");
        when(fieldDescribe.getType()).thenReturn(IFieldType.TEXT);

        // 准备测试数据
        String expression = "name LIKE 'test'";

        // 模拟表达式服务返回
        when(expressionService.evaluate(anyString(), eq(objectData))).thenReturn(true);

        // 执行测试
        Boolean result = sfaExpressionService.evaluate(expression, objectData, objectDescribe);

        // 验证结果
        assertNotNull(result);
        assertTrue(result);

        // 验证表达式服务被调用
        verify(expressionService, times(1)).evaluate(anyString(), eq(objectData));
    }
    
    @Test
    void testEvaluate_EmptyExpression() {
        // 执行测试
        Boolean result = sfaExpressionService.evaluate("", objectData, objectDescribe);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result); // 空表达式应该返回true
        
        // 验证表达式服务没有被调用
        verify(expressionService, never()).evaluate(anyString(), any());
    }
    
    @Test
    void testEvaluate_NullExpression() {
        // 执行测试
        Boolean result = sfaExpressionService.evaluate(null, objectData, objectDescribe);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result); // null表达式应该返回true
        
        // 验证表达式服务没有被调用
        verify(expressionService, never()).evaluate(anyString(), any());
    }
}
