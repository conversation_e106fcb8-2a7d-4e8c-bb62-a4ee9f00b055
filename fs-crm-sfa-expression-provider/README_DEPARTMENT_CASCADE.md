# 部门级联过滤功能实现说明

## 功能概述

本功能为 SFA 表达式服务添加了部门级联过滤支持，当部门字段的过滤条件设置了 `is_cascade: true` 时，会自动包含该部门的所有子部门数据。

## 核心特性

- 支持部门字段的级联查询（包含子部门）
- 支持 `IN` 和 `NIN` 操作符的级联处理
- 自动识别部门字段类型
- 提供两种实现方案：表达式函数和预展开子部门ID

## 使用方式

### 前端请求示例

```json
{
  "filters": [
    {
      "field_name": "owner_department",
      "field_values": ["1000"],
      "operator": "IN",
      "is_cascade": true,
      "value_type": 0
    }
  ]
}
```

### 参数说明

- `field_name`: 部门字段名称（如 `owner_department`）
- `field_values`: 部门ID列表
- `operator`: 操作符，支持 `IN`（属于）和 `NIN`（不属于）
- `is_cascade`: 是否包含子部门，设置为 `true` 启用级联查询
- `value_type`: 值类型，保持为 0

## 实现原理

### 1. 过滤器处理流程

1. **字段类型识别**：通过 `ObjectDescribe` 判断字段是否为部门类型
2. **级联标志检查**：检查 `IFilter.getIsCascade()` 是否为 `true`
3. **表达式构建**：根据操作符构建相应的级联表达式

### 2. 表达式转换

#### 原始逻辑（不包含子部门）
```java
// IN 操作符
"(!ISNULL(owner_department) && CONTAINS(',1000,',owner_department))"

// NIN 操作符  
"!(!ISNULL(owner_department) && CONTAINS(',1000,',owner_department))"
```

#### 级联逻辑（包含子部门）
```java
// IN 操作符 + is_cascade: true
"(!ISNULL(owner_department) && INDEPARTMENT(owner_department,'1000'))"

// NIN 操作符 + is_cascade: true
"(ISNULL(owner_department) || !INDEPARTMENT(owner_department,'1000'))"
```

### 3. 两种实现方案

#### 方案1：表达式函数（推荐）
- 使用自定义的 `INDEPARTMENT` 函数
- 在表达式引擎中注册函数处理逻辑
- 运行时动态获取子部门数据

#### 方案2：预展开子部门ID
- 在构建表达式前获取所有子部门ID
- 将子部门ID展开到 `field_values` 中
- 使用原有的 `CONTAINS` 逻辑处理

## 核心类说明

### 1. SFAExpressionServiceImpl
- **位置**: `com.facishare.crm.sfa.expression.SFAExpressionServiceImpl`
- **功能**: 主要的表达式处理服务
- **关键方法**:
  - `getOrgExpression()`: 处理人员和部门字段的表达式
  - `isDepartmentField()`: 判断是否为部门字段
  - `buildCascadeDepartmentExpression()`: 构建级联部门表达式

### 2. DepartmentCascadeService
- **位置**: `com.facishare.crm.sfa.expression.service.DepartmentCascadeService`
- **功能**: 部门级联查询服务接口
- **关键方法**:
  - `getAllSubDepartmentIds()`: 获取所有子部门ID
  - `isEmployeeInDepartment()`: 判断员工是否属于部门
  - `isDepartmentInParent()`: 判断部门是否属于父部门

### 3. DepartmentExpressionFunction
- **位置**: `com.facishare.crm.sfa.expression.function.DepartmentExpressionFunction`
- **功能**: 部门相关的表达式函数扩展
- **关键方法**:
  - `inDepartment()`: INDEPARTMENT 函数的实现

## 配置和部署

### 1. 依赖注入配置

```java
@Service
public class SFAExpressionServiceImpl implements SFAExpressionService {
    
    @Autowired
    private DepartmentCascadeService departmentCascadeService;
    
    // ... 其他代码
}
```

### 2. 表达式函数注册

如果使用方案1（表达式函数），需要在表达式引擎中注册 `INDEPARTMENT` 函数：

```java
// 在表达式引擎初始化时注册函数
expressionEngine.registerFunction("INDEPARTMENT", departmentExpressionFunction::inDepartment);
```

## 测试

### 单元测试
- 位置: `SFAExpressionServiceImplTest`
- 覆盖场景: 级联和非级联、IN和NIN操作符、部门和非部门字段

### 集成测试建议
1. 创建测试部门层级结构
2. 测试不同操作符的级联效果
3. 验证性能影响

## 注意事项

### 1. 性能考虑
- 级联查询可能涉及大量子部门，注意查询性能
- 建议对部门层级关系进行缓存
- 考虑异步处理大量部门数据

### 2. 数据一致性
- 确保部门层级关系数据的实时性
- 处理部门结构变更的影响

### 3. 兼容性
- 保持向后兼容，`is_cascade` 默认为 `false`
- 非部门字段忽略 `is_cascade` 参数

## 后续优化

1. **缓存优化**: 对部门层级关系进行缓存
2. **批量处理**: 支持批量获取多个部门的子部门
3. **权限控制**: 结合用户权限过滤可见的部门范围
4. **监控告警**: 添加性能监控和异常告警

## 联系方式

如有问题，请联系开发团队或查看相关文档。
