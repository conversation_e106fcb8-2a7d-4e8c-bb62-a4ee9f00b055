package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.dto.LayoutResult;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.Builder;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.core.predef.action.AbstractDesignerSaveLayoutAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.AbstractDesignerSaveLayoutAction.Result;

/**
 * Created by zhaooju on 2023/11/9
 */
public abstract class AbstractDesignerSaveLayoutAction extends PreDefineAction<Arg, Result> {
    protected ILayout newLayout;
    protected IObjectDescribe newDescribe;
    protected ObjectDescribeDocument describeExtra;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Collections.emptyList();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validate();
    }

    protected void validate() {

    }

    @Override
    protected void init() {
        super.init();
        newLayout = arg.getLayoutData().toLayout();
        newDescribe = arg.getDescribeData().toObjectDescribe();
        describeExtra = arg.getDescribeExtra();
    }

    @Override
    protected Result doAct(Arg arg) {
        LayoutExt layoutExt = LayoutExt.of(newLayout);
        layoutExt.fixUIEvent();
        if (layoutExt.isListLayout()) {
            return saveListLayout();
        }
        if (layoutExt.isMobileListLayout()) {
            return saveMobileListLayout();
        }
        return saveLayout();
    }

    /**
     * 保存布局的默认方法
     *
     * @return 返回保存的布局
     */
    protected abstract Result saveLayout();

    /**
     * 保存移动端摘要布局
     *
     * @return 返回保存的布局
     */
    protected abstract Result saveMobileListLayout();

    /**
     * 保存列表页布局
     *
     * @return 返回保存的布局
     */
    protected abstract Result saveListLayout();

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(actionContext.getUser(), arg.getAppId());
    }

    protected ObjectDescribeDocument updateDescribeExtra(User user, String describeAPIName, ObjectDescribeDocument argDescribeExtra) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId()) || CollectionUtils.empty(argDescribeExtra)) {
            return null;
        }
        DescribeExtra describeExtra = serviceFacade.updateDescribeExtra(user, describeAPIName, argDescribeExtra);
        return ObjectDescribeDocument.of(describeExtra);
    }

    protected Result buildResult(LayoutResult layoutResult, ObjectDescribeDocument describeExtra, List<Map<String, Object>> fieldsExtra) {
        return buildResult(layoutResult.getObjectDescribe(), layoutResult.getLayout(), describeExtra, fieldsExtra);
    }

    protected Result buildResult(ILayout layout) {
        return buildResult(null, layout, null, null);
    }

    private Result buildResult(IObjectDescribe describe, ILayout layout, ObjectDescribeDocument describeExtra, List<Map<String, Object>> fieldsExtra) {
        return Result.builder()
                .layout(LayoutDocument.of(layout))
                .objectDescribe(ObjectDescribeDocument.of(describe))
                .describeExtra(describeExtra)
                .fieldsExtra(fieldsExtra)
                .build();
    }


    @Data
    public static class Arg {
        private LayoutDocument layoutData;
        private ObjectDescribeDocument describeData;
        private ObjectDescribeDocument describeExtra;
        private String persistentDataCalc;
        private List<Map<String, Object>> fieldsExtra;
        /**
         * 应用Id
         */
        private String appId;
    }

    @Data
    @Builder
    public static class Result {
        private LayoutDocument layout;
        private ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument describeExtra;
        private List<Map<String, Object>> fieldsExtra;
    }
}
